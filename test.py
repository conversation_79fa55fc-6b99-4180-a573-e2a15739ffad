import time
import random
import base64
from Crypto.Cipher import SM4
from Crypto.Util.Padding import pad

# --- 从JS文件中提取的常量 ---
# SM4加密的密钥和初始向量，必须是bytes类型
KEY = b"11HDESaAhiHHugDz"
IV = b"UISwD9fW6cFh9SNS"

def generate_request_id():
    """
    使用 pycryptodome 库实现 SM4/CBC/PKCS7Padding 加密来生成 requestID。
    """
    # 步骤 1: 生成一个1000到9999之间的随机整数，并转为字符串
    random_num_str = str(random.randint(1000, 9999))

    # 步骤 2: 获取当前毫秒级时间戳，并转为字符串
    timestamp_str = str(int(time.time() * 1000))

    # 步骤 3: 构造待加密的明文
    plaintext = random_num_str + timestamp_str
    print(f"[*] 待加密明文: {plaintext}")

    # 步骤 4: SM4 加密与 Base64 编码
    # 初始化 SM4 Cipher
    cipher = SM4.new(key=KEY, mode=SM4.MODE_CBC, iv=IV)

    # 对明文进行 PKCS7 填充，使其长度是16字节的倍数
    padded_data = pad(plaintext.encode('utf-8'), SM4.block_size, style='pkcs7')
    
    # 加密
    encrypted_bytes = cipher.encrypt(padded_data)
    
    # 将加密后的字节流进行 Base64 编码
    encrypted_base64_str = base64.b64encode(encrypted_bytes).decode('utf-8')
    print(f"[*] Base64密文: {encrypted_base64_str}")

    # 步骤 5: 拼接最终的 requestID
    request_id = random_num_str + encrypted_base64_str
    
    return request_id

if __name__ == "__main__":
    request_id = generate_request_id()
    
    print("\n" + "="*30)
    print(f"✅ 生成的 RequestID: {request_id}")
    print("="*30)